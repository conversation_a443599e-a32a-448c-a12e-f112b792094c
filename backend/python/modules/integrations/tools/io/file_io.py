import os
from utils.convert import safe_parse_xml_with_preprocessing, extract_text_from_xml_element


class FileIOTool:
    def __init__(self, repo_path: str):
        self.repo_path = repo_path

    def read(self, query: str) -> str:
        """
        读取文件内容

        Args:
            query: 查询字符串

        Returns:
            str: 文件内容
        """
        # 解析查询参数
        params = self._parse_query(query)

        # 检查文件路径是否存在
        if not params['file_path']:
            return "Error: No file path specified"

        try:
            # 调用内部读取方法
            return self._read_file(
                file_path=params['file_path'],
                start_line=params['start_line'],
                end_line=params['end_line']
            )
        except FileNotFoundError:
            return f"Error: File '{params['file_path']}' not found"
        except Exception as e:
            return f"Error reading file: {str(e)}"

    def write(self, query: str, **kwargs: dict) -> None:
        # TODO: 暂时用不上
        pass

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'file_path': '',
            'start_line': 0,
            'end_line': 200
        }

        # 尝试解析XML格式
        try:
            root = safe_parse_xml_with_preprocessing(query)
            if root is not None:
                # 查找file_io元素
                file_io_elem = root.find('.//file_io')
                if file_io_elem is not None:
                    # 解析path
                    path_elem = file_io_elem.find('path')
                    if path_elem is not None:
                        params['file_path'] = extract_text_from_xml_element(path_elem).strip()

                    # 解析start_line
                    start_line_elem = file_io_elem.find('start_line')
                    if start_line_elem is not None:
                        try:
                            params['start_line'] = int(extract_text_from_xml_element(start_line_elem).strip())
                        except ValueError:
                            pass

                    # 解析end_line
                    end_line_elem = file_io_elem.find('end_line')
                    if end_line_elem is not None:
                        try:
                            params['end_line'] = int(extract_text_from_xml_element(end_line_elem).strip())
                        except ValueError:
                            pass
        except Exception:
            # XML解析失败，尝试作为普通文本处理
            # 假设普通文本就是文件路径
            params['file_path'] = query.strip()

        return params
    
    def _read_file(self, file_path: str, start_line: int=0, end_line: int=200) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            start_line: 起始行号
            end_line: 结束行号
            
        Returns:
            str: 文件内容
        """
        with open(os.path.join(self.repo_path, file_path), 'r', encoding='utf-8') as f:
            lines = f.readlines()
            return "\n".join(lines[max(0, start_line - 1):min(len(lines), end_line)])
    
    def _write_replace(self, file_path: str, content: str) -> None:
        """
        写入文件内容
        
        Args:
            file_path: 文件路径
            content: 文件内容
        """
        with open(os.path.join(self.repo_path, file_path), 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _write_append(self, file_path: str, content: str) -> None:
        """
        追加文件内容
        
        Args:
            file_path: 文件路径
            content: 文件内容
        """
        with open(os.path.join(self.repo_path, file_path), 'a', encoding='utf-8') as f:
            f.write(content)
    
    @property
    def description(self):
        return """- `file_io`: File I/O operations tool for reading file content.

  Parameters:
  - `path` (required): Relative file path from repository root
  - `start_line` (optional): Starting line number for reading (default: 1, 1-based indexing)
  - `end_line` (optional): Ending line number for reading (default: 200)

  Use cases:
  - Read entire files or specific line ranges for code analysis
  - Inspect configuration files, source code, or documentation
"""
    
    @property
    def examples(self):
        return """
<output>
    <file_io>
    <path>src/main.py</path>
    <start_line>1</start_line>
    <end_line>10</end_line>
    </file_io>

    <file_io>
    <path>src/main.py</path>
    </file_io>
</output>
"""