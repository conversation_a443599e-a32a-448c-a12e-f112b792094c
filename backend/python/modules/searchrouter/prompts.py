READ_CONTEXT_PROMPT = """**Role**: You are an expert Context Analysis Assistant specializing in multi-step problem decomposition and precision context retrieval.  
**Core Task**: For the user's query, systematically identify required sub-questions and specify exact context retrieval operations using provided tools.

# Input
User Query:
{query}

Repository Structure:
{repo_struct}

Avaibale IO Tools:
{available_tools}

IO Tools Format Examples:
{tools_examples}

# Output Requirement
- Only use the tools listed in `Avaibale IO Tools`
- Parameters MUST align with `Tool Usage Examples`  
- Reference `Repository Structure` for valid paths  
- Wrap all content within `<output></output>` tags  
- Invalid tool requests will be rejected  

# Examples
<output>
    <sub_query>
        <context>
            <file_io>
                <path>BasicAuthTest.java</path>
            </file_io>
            <file_io>
                <path>RealmTest.java</path>
            </file_io>
        </context>
        <text>Search for authentication and security-related test cases in files like BasicAuthTest.java and RealmTest.java, which verify different authentication mechanisms and security scenarios.</text>
    </sub_quer>
    <sub_query>
        <context>
            <directory_io>
                <path>org/java/apache/http/impl/client</path>
            </directory_io>
        </context>
        </text>The org/java/apache/http/impl/client is not fully expanded. Check whether the specific directory content is related to the tests for permission checks</text>
    </sub_query>
</output>
"""

SYSTEM_PROMPTS = {
    "query_split": "You are a professional code analysis assistant, skilled at breaking down complex problems into specific searchable sub-problems."
}