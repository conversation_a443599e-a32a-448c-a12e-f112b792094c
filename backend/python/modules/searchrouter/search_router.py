# 1. 用户的输入问题
# 2. 问题发散并决定调用工具
# 3. 并发调用 ToolCall
    # 3.1 EmbeddingSearch (input: new query, repo basic info, output: code snippets )
        # 3.1.1 问题重写 -> 查询的代码/文档的描述性文字
        # 3.1.2 是否被Emedding过？没有则对当前仓库进行Embedding
        # 3.1.3 调用远程Embedding服务进行搜索
        # 3.1.4 代码片段去重
    # 3.2 Term-Parse Search (input: query, output: code snippets )
        # 3.2.1 问题重写 -> 重写为可能出现在文档中的描述或者代码
        # 3.2.2 是否有本地缓存信息？有则加载，无则对当前仓库进行索引
        # 3.2.3 代码片段去重
    # 3.3 GrepSearch ( input: grep keywords, output: code snippets )
        # 3.3.1 问题重写 -> 重写为可被grep搜索到的关键词
        # 3.3.2 执行grep搜索
        # 3.3.3 代码片段去重
    # 3.4 ReadFile( input: file path, output: file content )
        # 3.4.1 读取文件内容
    # 3.5 ReadDir( input: dir path, output: file list )
        # 3.5.1 读取目录内容
    # 3.6 GraphSerch( input: node, search type, search depth, output: code snippets )
        # 3.6.1 问题重写 -> 重写为可被图搜索理解的查询
        # 3.6.2 是否有本地图信息？有则加载，无则对当前仓库进行图索引
        # 3.6.3 代码片段去重
# 4. 汇总结果
# 5. 判断是否符合需要

from typing import List
from modules.llm.llm_client import LLMClient, default_llm_client
from utils.file import FileNode, build_file_tree
from core.config import get_config
from modules.common.constant import FileFilterMode, SearchToolEnum
from modules.integrations.tools.search.any_search import get_search_tool_instance
from modules.integrations.tools.io.any_io import AnyIOTool
from modules.common.schema import SearchResult, SubQuery
from modules.searchrouter.prompts import READ_CONTEXT_PROMPT, SYSTEM_PROMPTS

class SearchRouter:
    def __init__(
        self,
        repo_path: str,
        repo_info: str = "",
        llm_client: LLMClient = None,
        search_tool: SearchToolEnum = SearchToolEnum.ANY
    ):
        self.side_memory: List[str]  = [] # 旁路记忆，可以用于暂存从IDE中获取的信息

        self.repo_info = repo_info # 仓库信息
        self.repo_path = repo_path # 搜索目标路径
        
        self.search_tool = search_tool # 搜索工具类型

        self.search_instance = get_search_tool_instance(
            SearchToolEnum.ANY, # 使用ANY类型的Search工具来中转对其他工具的调用，避免直接初始化对应的search工具，通过这种方式统一工具调用的输入入口
            repo_path, 
            enabled_search_tools=[search_tool.value] if search_tool != SearchToolEnum.ANY else get_config().deepsearch.enabled_search_tools) # 可使用的搜索工具，默认是ANY，此时按照配置文件中的设置来确定可用的搜索工具
        self.io_instance = AnyIOTool(repo_path=repo_path)

        self.llm_client = llm_client or default_llm_client

    async def search_async(self, query: str) -> SearchResult:
        # 1. 读取所需要的信息，循环MAX_READ_ITERATION次（default=1）
        # 获取当前仓库树结构
        repo_node = FileNode(path=self.repo_path, 
                             name=self.repo_path.split("/")[-1], 
                             type="directory", 
                             children=build_file_tree(root_dir=self.repo_path, start_dir=self.repo_path, max_leaf_nodes=100, filter_mode=FileFilterMode.LOCAL))
        
        sub_queries = self._split_queris(query, repo_node)
        
        # 2. 拆分查询方向，并选定搜索工具
        
        # 3. 并发搜索

        # 4. 搜索结果合并去重
        
        # 5. 如果当前循环搜索次数超过MAX_SEARCH_ITERATION（default=1）则结束搜索,否则回到1

        # 6. 重排序

        pass

    async def _split_queris(self, query: str, repo_struct: str) -> List[SubQuery]:
        prompt = READ_CONTEXT_PROMPT.format(
            query=query,
            repo_struct=repo_struct,
            available_tools=self.io_instance.description,
            tools_examples=self.io_instance.examples
        )

        response_text = self.llm_client.call_async(prompt, SYSTEM_PROMPTS['query_split'], stream=False)
        return SubQuery.parse_response_text(response_text)

    async def 