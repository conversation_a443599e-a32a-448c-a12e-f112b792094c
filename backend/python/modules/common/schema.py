from typing import List, Dict, Tuple
from pydantic import BaseModel, Field, ConfigDict
from modules.common.constant import IOToolEnum
from utils.convert import safe_extract_xml_tags_with_preprocessing, extract_text_from_xml_element, safe_parse_xml_with_preprocessing

class CodeSnippet(BaseModel):
    """代码片段数据结构"""
    file_path: str = Field(..., description="文件路径", min_length=1)
    start_line: int = Field(..., description="行号", ge=0) # 0-based    
    end_line: int = Field(..., description="行号", ge=0) # 0-based
    content: str = Field(..., description="代码内容", min_length=1)
    context_before: str = Field(default="", description="前置上下文")
    context_after: str = Field(default="", description="后置上下文")
    score: float = Field(default=0.0, description="相关性分数")

    def get_full_content(self) -> str:
        """获取完整代码内容，包括上下文"""
        return f"{self.context_before}\n{self.content}\n{self.context_after}"

class SearchResult(BaseModel):
    """搜索结果数据结构"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        frozen=False
    )

    original_query: str = Field(..., description="原始查询字符串", min_length=1)

    all_queries: List[str] = Field(
        default_factory=list,
        description="包含生成的新查询的所有查询列表"
    )
    
    code_snippets: List[CodeSnippet] = Field(
        default_factory=list,
        description="搜索到的代码片段列表"
    )

    iterations: int = Field(
        default=0,
        description="搜索迭代次数",
        ge=0
    )

    def get_summary(self) -> str:
        """获取搜索结果摘要"""
        return f"""搜索摘要:
- 原始查询: {self.original_query}
- 总查询数量: {len(self.all_queries)}
- 找到的代码片段: {len(self.code_snippets)}
- 迭代次数: {self.iterations}
"""
    
class SubQuery(BaseModel):
    text: str = Field(..., description="子查询文本")
    context_operations: List[Tuple[IOToolEnum, str, str]] = Field(..., description="上下文操作列表")

    @staticmethod
    def parse_response_text(response_text: str) -> List['SubQuery']:
        """
        解析响应文本，提取子查询信息

        Args:
            response_text: 包含XML格式的响应文本

        Returns:
            List[SubQuery]: 解析出的子查询列表

        Examples:
            >>> response = '''<output>
            ...     <sub_query>
            ...         <context>
            ...             <file_io><path>test.py</path></file_io>
            ...         </context>
            ...         <text>Search for test functions</text>
            ...     </sub_query>
            ... </output>'''
            >>> queries = SubQuery.parse_response_text(response)
            >>> print(len(queries))
            1
        """
        sub_queries = []

        # 使用安全的XML标签提取函数
        extracted_tags = safe_extract_xml_tags_with_preprocessing(response_text, ['sub_query'])

        for _, tag_content in extracted_tags:
            try:
                # 解析单个sub_query标签
                root = safe_parse_xml_with_preprocessing(tag_content)
                if root is None:
                    continue

                # 提取text内容
                text_elem = root.find('text')
                if text_elem is None:
                    continue

                text_content = extract_text_from_xml_element(text_elem)
                if not text_content:
                    continue

                # 提取context操作
                context_operations = []
                context_elem = root.find('context')
                if context_elem is not None:
                    # 查找file_io操作
                    for file_io_elem in context_elem.findall('file_io'):
                        path_elem = file_io_elem.find('path')
                        if path_elem is not None:
                            path_content = extract_text_from_xml_element(path_elem)
                            if path_content:
                                context_operations.append((IOToolEnum.FILE, path_content, file_io_elem))

                    # 查找directory_io操作
                    for dir_io_elem in context_elem.findall('directory_io'):
                        path_elem = dir_io_elem.find('path')
                        if path_elem is not None:
                            path_content = extract_text_from_xml_element(path_elem)
                            if path_content:
                                context_operations.append((IOToolEnum.DIRECTORY, path_content))

                # 创建SubQuery对象
                sub_query = SubQuery(
                    text=text_content,
                    context_operations=context_operations
                )
                sub_queries.append(sub_query)

            except Exception:
                # 如果解析单个sub_query失败，跳过并继续处理下一个
                continue

        return sub_queries
