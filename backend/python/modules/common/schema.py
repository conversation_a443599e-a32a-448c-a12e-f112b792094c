from typing import List, Dict, Tuple, Union
import xml.etree.ElementTree as ET
import re
from pydantic import BaseModel, Field, ConfigDict
from modules.common.constant import IOToolEnum, SearchToolEnum
from utils.convert import safe_extract_xml_tags_with_preprocessing, extract_text_from_xml_element, safe_parse_xml_with_preprocessing

class CodeSnippet(BaseModel):
    """代码片段数据结构"""
    file_path: str = Field(..., description="文件路径", min_length=1)
    start_line: int = Field(..., description="行号", ge=0) # 0-based    
    end_line: int = Field(..., description="行号", ge=0) # 0-based
    content: str = Field(..., description="代码内容", min_length=1)
    context_before: str = Field(default="", description="前置上下文")
    context_after: str = Field(default="", description="后置上下文")
    score: float = Field(default=0.0, description="相关性分数")

    def get_full_content(self) -> str:
        """获取完整代码内容，包括上下文"""
        return f"{self.context_before}\n{self.content}\n{self.context_after}"

class SearchResult(BaseModel):
    """搜索结果数据结构"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        frozen=False
    )

    original_query: str = Field(..., description="原始查询字符串", min_length=1)

    all_queries: List[str] = Field(
        default_factory=list,
        description="包含生成的新查询的所有查询列表"
    )
    
    code_snippets: List[CodeSnippet] = Field(
        default_factory=list,
        description="搜索到的代码片段列表"
    )

    iterations: int = Field(
        default=0,
        description="搜索迭代次数",
        ge=0
    )

    def get_summary(self) -> str:
        """获取搜索结果摘要"""
        return f"""搜索摘要:
- 原始查询: {self.original_query}
- 总查询数量: {len(self.all_queries)}
- 找到的代码片段: {len(self.code_snippets)}
- 迭代次数: {self.iterations}
"""

class ContextOperation(BaseModel):
    tool: Union[IOToolEnum, SearchToolEnum] = Field(..., description="上下文操作工具")
    xml_content: str = Field(..., description="上下文操作XML内容")
    context_uri: str = Field(..., description="上下文操作URI")


class SearchQuery(BaseModel):
    text: str = Field(..., description="子查询文本")
    context_operations: List[ContextOperation] = Field(..., description="上下文操作列表")

    @staticmethod
    def parse_response_text(response_text: str) -> List['SearchQuery']:
        """
        解析响应文本，提取子查询信息

        Args:
            response_text: 包含XML格式的响应文本

        Returns:
            List[SearchQuery]: 解析出的子查询列表

        Examples:
            >>> response = '''<output>
            ...     <sub_query>
            ...         <context>
            ...             <file_io><path>test.py</path></file_io>
            ...         </context>
            ...         <text>Search for test functions</text>
            ...     </sub_query>
            ... </output>'''
            >>> queries = SearchQuery.parse_response_text(response)
            >>> print(len(queries))
        """
        search_queries = []

        # 使用安全的XML标签提取函数
        extracted_tags = safe_extract_xml_tags_with_preprocessing(response_text, ['sub_query'])

        for _, tag_content in extracted_tags:
            try:
                # 解析单个sub_query标签
                root = safe_parse_xml_with_preprocessing(tag_content)
                if root is None:
                    continue

                # 提取text内容
                text_elem = root.find('text')
                if text_elem is None:
                    continue

                text_content = extract_text_from_xml_element(text_elem)
                if not text_content:
                    continue

                # 提取context操作
                context_operations: List[ContextOperation] = []
                context_elem = root.find('context')
                if context_elem is not None:
                    # 查找file_io操作
                    for file_io_elem in context_elem.findall('file_io'):
                        path_elem = file_io_elem.find('path')
                        if path_elem is not None:
                            path_content = extract_text_from_xml_element(path_elem)
                            if path_content:
                                # 将XML元素转换为字符串
                                file_io_xml = ET.tostring(file_io_elem, encoding='unicode')
                                context_operations.append(ContextOperation(IOToolEnum.FILE, file_io_xml, path_content))

                    # 查找directory_io操作
                    for dir_io_elem in context_elem.findall('directory_io'):
                        path_elem = dir_io_elem.find('path')
                        if path_elem is not None:
                            path_content = extract_text_from_xml_element(path_elem)
                            if path_content:
                                # 将XML元素转换为字符串
                                dir_io_xml = ET.tostring(dir_io_elem, encoding='unicode')
                                context_operations.append(ContextOperation(IOToolEnum.DIRECTORY, dir_io_xml, path_content))

                # 创建SubQuery对象
                sub_query = SearchQuery(
                    text=text_content,
                    context_operations=context_operations
                )
                search_queries.append(sub_query)

            except Exception:
                # 如果解析单个sub_query失败，跳过并继续处理下一个
                continue

        # TODO: 增加对search内容返回格式的解析
        # 解析 _extract_structure 类型的返回格式
        structure_results = _extract_structure_format(response_text)
        for search_tool_enum, query_content in structure_results:
            try:
                # 为每个搜索工具查询创建一个SearchQuery对象
                # context_uri设为空，因为这种格式没有特定的上下文操作
                context_operation = ContextOperation(
                    tool=search_tool_enum,
                    xml_content=query_content,
                    context_uri=""
                )

                # 从XML内容中提取文本作为查询文本
                # 如果是纯文本，直接使用；如果是XML，尝试提取内容
                text_content = query_content
                if query_content.startswith('<') and query_content.endswith('>'):
                    # 尝试从XML中提取文本内容
                    try:
                        root = safe_parse_xml_with_preprocessing(query_content)
                        if root is not None:
                            # 提取所有文本内容
                            extracted_text = extract_text_from_xml_element(root)
                            if extracted_text:
                                text_content = extracted_text
                    except Exception:
                        # 如果XML解析失败，使用原始内容
                        pass

                search_query = SearchQuery(
                    text=text_content,
                    context_operations=[context_operation]
                )
                search_queries.append(search_query)

            except Exception:
                # 如果创建SearchQuery失败，跳过并继续处理下一个
                continue

        return search_queries


def _extract_structure_format(response_text: str) -> List[Tuple[SearchToolEnum, str]]:
    """
    从LLM回复中提取元信息和结构化内容，参考deep_search._extract_structure的实现

    Args:
        response_text: 待提取的文本

    Returns:
        List[Tuple[SearchToolEnum, str]]: 包含搜索工具类型和查询内容的元组列表
    """
    # 提取<output>标签内的内容
    output_pattern = r'<output>(.*?)</output>'
    output_match = re.search(output_pattern, response_text, re.DOTALL)

    if not output_match:
        return []

    output_content = output_match.group(1).strip()

    if not output_content:
        return []

    results = []

    # 遍历所有搜索工具枚举，动态匹配标签
    for tool_enum in SearchToolEnum:
        # 跳过不需要匹配的工具类型
        if tool_enum in [SearchToolEnum.ANY, SearchToolEnum.EMBEDDING]:
            continue

        tool_tag = tool_enum.value
        pattern = f'<{tool_tag}>(.*?)</{tool_tag}>'
        matches = re.findall(pattern, output_content, re.DOTALL)

        if matches:
            # 为每个匹配创建一个元组
            for match in matches:
                query_content = match.strip()
                if query_content:
                    results.append((tool_enum, f"<{tool_tag}>{query_content}</{tool_tag}>"))

    if results:
        return results

    # 如果没有匹配到任何工具标签，尝试提取纯文本查询
    lines = [line.strip() for line in output_content.split('\n') if line.strip()]
    if lines:
        # 将纯文本查询作为 term_sparse 查询返回
        return [(SearchToolEnum.TERM_SPRSE, line) for line in lines]

    return []